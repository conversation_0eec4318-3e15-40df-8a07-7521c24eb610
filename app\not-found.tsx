import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Home, ArrowRight, BookOpen } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4" dir="rtl">
      <div className="max-w-2xl w-full text-center">
        {/* الرقم 404 */}
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4">
            404
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
        </div>

        {/* الرسالة الرئيسية */}
        <Card className="mb-8 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <div className="mb-6">
              <div className="text-6xl mb-4">📚</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4 arabic-heading">
                عذراً، لم نتمكن من العثور على هذه الصفحة
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                يبدو أن الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                <br />
                لا تقلق، يمكنك العودة إلى الصفحة الرئيسية أو استكشاف المحتوى التعليمي المتاح.
              </p>
            </div>

            {/* أزرار التنقل */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2"
                >
                  <Home className="w-5 h-5" />
                  العودة للصفحة الرئيسية
                  <ArrowRight className="w-5 h-5 rtl:rotate-180" />
                </Button>
              </Link>

              <Link href="/levels">
                <Button 
                  variant="outline" 
                  size="lg"
                  className="border-2 border-blue-200 hover:border-blue-400 text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-xl transition-all duration-300 flex items-center gap-2"
                >
                  <BookOpen className="w-5 h-5" />
                  استكشاف المستويات التعليمية
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* اقتراحات مفيدة */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0 shadow-md">
          <CardContent className="p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4 arabic-heading">
              💡 اقتراحات مفيدة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-right">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700">
                  تحقق من صحة الرابط المكتوب في شريط العناوين
                </p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700">
                  استخدم القائمة الرئيسية للتنقل بين الأقسام
                </p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700">
                  ابحث عن المحتوى المطلوب من خلال المستويات التعليمية
                </p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700">
                  تواصل معنا إذا كنت تواجه مشكلة تقنية
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* رسالة تشجيعية */}
        <div className="mt-8 text-gray-600">
          <p className="text-lg">
            &ldquo;التعلم رحلة مستمرة، وكل خطوة تقربك من هدفك&rdquo; 🌟
          </p>
        </div>
      </div>
    </div>
  )
}
