'use client';

import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import type { Lesson } from '@/data/types';

interface LessonCardProps {
  lesson: Lesson;
}

const LessonCard = ({ lesson }: LessonCardProps) => {
  // Determine the correct route based on content type
  const getRoute = () => {
    switch (lesson.content_type) {
      case 'homework':
        return `/homework/${lesson.id}`;
      case 'doros':
        return `/summary/${lesson.id}`;
      case 'tamarin':
      default:
        return `/lesson/${lesson.id}`;
    }
  };

  return (
    <Link href={getRoute()} className="block" dir="rtl">
      <Card className="card-hover rtl-card hover:bg-card/80 transition-colors">
        <CardContent className="p-6 flex items-center rtl-flex-row-reverse">
          <div className="mr-4 bg-muted rounded-full p-2">
            <ChevronRight className="h-5 w-5 text-primary rtl:rotate-180" />
          </div>
          <div className="flex-1 text-right flex items-center">
            <h3 className="text-xl font-bold text-primary text-right arabic-heading">{lesson.title}</h3>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default LessonCard;
