import { MetadataRoute } from 'next'
import { siteConfig } from '@/lib/config'
import {
  fetchLevelsFromSupabase,
  fetchYearsFromSupabase,
  fetchSubjectsFromSupabase,
  fetchLessonsFromSupabase
} from '@/backend/utils/supabaseLoader'

const baseUrl = siteConfig.url

// إعادة التحقق من sitemap كل ساعة (3600 ثانية)
export const revalidate = 3600

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Static pages for Moroccan education platform
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/levels`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
  ]

  try {
    // جلب البيانات الديناميكية من Supabase مباشرة
    const [levels, years, subjects, lessons] = await Promise.all([
      fetchLevelsFromSupabase(),
      fetchYearsFromSupabase(),
      fetchSubjectsFromSupabase(),
      fetchLessonsFromSupabase()
    ])

    console.log(`Sitemap: تم جلب ${levels.length} مستويات، ${years.length} سنوات، ${subjects.length} مواد، ${lessons.length} دروس`)

    // إضافة صفحات السنوات الدراسية
    const yearPages: MetadataRoute.Sitemap = years.map(year => ({
      url: `${baseUrl}/year/${year.id}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))

    // إضافة صفحات المواد الدراسية
    const subjectPages: MetadataRoute.Sitemap = subjects.map(subject => ({
      url: `${baseUrl}/subject/${subject.id}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    // إضافة صفحات الدروس (فقط التي تحتوي على محتوى)
    const lessonPages: MetadataRoute.Sitemap = lessons
      .filter(lesson => lesson.exercises && lesson.exercises.length > 0)
      .map(lesson => ({
        url: `${baseUrl}/lesson/${lesson.id}`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      }))

    // إضافة صفحات الواجبات (فقط التي تحتوي على محتوى)
    const homeworkPages: MetadataRoute.Sitemap = lessons
      .filter(lesson =>
        lesson.content_type === 'homework' &&
        lesson.exercises &&
        lesson.exercises.length > 0
      )
      .map(lesson => ({
        url: `${baseUrl}/homework/${lesson.id}`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      }))

    // إضافة صفحات الملخصات (فقط التي تحتوي على محتوى)
    const summaryPages: MetadataRoute.Sitemap = lessons
      .filter(lesson =>
        lesson.content_type === 'doros' &&
        lesson.exercises &&
        lesson.exercises.length > 0
      )
      .map(lesson => ({
        url: `${baseUrl}/summary/${lesson.id}`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      }))

    // إضافة صفحات الامتحانات (فقط التي تحتوي على محتوى)
    const examPages: MetadataRoute.Sitemap = lessons
      .filter(lesson =>
        lesson.content_type === 'exam' &&
        lesson.exercises &&
        lesson.exercises.length > 0
      )
      .map(lesson => ({
        url: `${baseUrl}/exam/${lesson.id}`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      }))

    const allPages = [
      ...staticPages,
      ...yearPages,
      ...subjectPages,
      ...lessonPages,
      ...homeworkPages,
      ...summaryPages,
      ...examPages
    ]

    console.log(`Sitemap: تم إنشاء ${allPages.length} صفحة في sitemap`)
    return allPages

  } catch (error) {
    console.error('خطأ في إنشاء sitemap:', error)
    // في حالة الخطأ، إرجاع الصفحات الثابتة فقط
    return staticPages
  }
}
