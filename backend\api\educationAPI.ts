import { Level, Year, Subject, Lesson, Exercise } from '@/data/types';
import {
  getLevels,
  getYears,
  getSubjects,
  getLessons,
  getYearsForLevel as fetchYearsForLevel,
  getSubjectsForYear as fetchSubjectsForYear,
  getLessonsForSubject as fetchLessonsForSubject,
  setDataSource,
  levels
} from '../utils/dataLoader';

// Export the function to set data source
export { setDataSource };

// Also export the raw data for backward compatibility
export { levels };

// وظيفة لمسح التخزين المؤقت وإعادة تحميل البيانات
import { clearAllData } from '../utils/storageManager';

export async function refreshAllData(): Promise<void> {
  // مسح التخزين المؤقت
  clearAllData();

  // مسح الذاكرة المؤقتة
  levelsCache = null;
  yearsCache = null;
  subjectsCache = null;
  lessonsCache = null;

  // إعادة تحميل البيانات الأساسية
  console.log("جاري إعادة تحميل البيانات...");
  await Promise.all([
    loadLevels(),
    loadYears(),
    loadSubjects(),
    loadLessons()
  ]);
  console.log("تم إعادة تحميل البيانات بنجاح");
}

// Cache for loaded data
let levelsCache: Level[] | null = null;
let yearsCache: Year[] | null = null;
let subjectsCache: Subject[] | null = null;
let lessonsCache: Lesson[] | null = null;

// Helper functions to load and cache data
async function loadLevels(): Promise<Level[]> {
  if (!levelsCache) {
    levelsCache = await getLevels();
    console.log(`تم تحميل ${levelsCache.length} مستويات دراسية`);
  }
  return levelsCache;
}

async function loadYears(): Promise<Year[]> {
  if (!yearsCache) {
    yearsCache = await getYears();
    console.log(`تم تحميل ${yearsCache.length} سنوات دراسية`);
  }
  return yearsCache;
}

async function loadSubjects(): Promise<Subject[]> {
  if (!subjectsCache) {
    subjectsCache = await getSubjects();
    console.log(`تم تحميل ${subjectsCache.length} مواد دراسية`);
  }
  return subjectsCache;
}

async function loadLessons(): Promise<Lesson[]> {
  if (!lessonsCache) {
    lessonsCache = await getLessons();
    console.log(`تم تحميل ${lessonsCache.length} دروس`);

    // عرض تفاصيل عن كل درس وتمارينه
    for (const lesson of lessonsCache) {
      console.log(`الدرس: ${lesson.title} - عدد التمارين: ${lesson.exercises.length}`);
    }
  }
  return lessonsCache;
}

// Functions to get full data objects with their relations
export async function getLevel(levelId: string): Promise<Level | undefined> {
  const levels = await loadLevels();
  return levels.find(level => level.id === levelId);
}

export async function getYear(yearId: string): Promise<Year | undefined> {
  const years = await loadYears();
  return years.find(year => year.id === yearId);
}

export async function getSubject(subjectId: string): Promise<Subject | undefined> {
  const subjects = await loadSubjects();
  return subjects.find(subject => subject.id === subjectId);
}

export async function getLesson(lessonId: string): Promise<Lesson | undefined> {
  const lessons = await loadLessons();
  const lesson = lessons.find(lesson => lesson.id === lessonId);

  // إضافة سجل لتفاصيل الدرس
  if (lesson) {
    console.log(`تم العثور على الدرس "${lesson.title}" مع ${lesson.exercises.length} تمارين`);
  } else {
    console.log(`لم يتم العثور على الدرس بالمعرف ${lessonId}`);
  }

  return lesson;
}

// دوال خاصة للاستخدام في بيئة الخادم (Server-Side) - تتجنب localStorage
import {
  getYearsServerSide,
  getSubjectsServerSide,
  getLevelsServerSide,
  getLessonsServerSide
} from '@/backend/utils/dataLoader';

export async function getYearServerSide(yearId: string): Promise<Year | undefined> {
  try {
    const years = await getYearsServerSide();
    return years.find(year => year.id === yearId);
  } catch (error) {
    console.error(`خطأ في جلب السنة ${yearId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getSubjectServerSide(subjectId: string): Promise<Subject | undefined> {
  try {
    const subjects = await getSubjectsServerSide();
    return subjects.find(subject => subject.id === subjectId);
  } catch (error) {
    console.error(`خطأ في جلب المادة ${subjectId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getLevelServerSide(levelId: string): Promise<Level | undefined> {
  try {
    const levels = await getLevelsServerSide();
    return levels.find(level => level.id === levelId);
  } catch (error) {
    console.error(`خطأ في جلب المستوى ${levelId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getLessonServerSide(lessonId: string): Promise<Lesson | undefined> {
  try {
    const lessons = await getLessonsServerSide();
    return lessons.find(lesson => lesson.id === lessonId);
  } catch (error) {
    console.error(`خطأ في جلب الدرس ${lessonId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getExercise(exerciseId: string): Promise<Exercise | undefined> {
  const lessons = await loadLessons();
  for (const lesson of lessons) {
    const exercise = lesson.exercises.find(ex => ex.id === exerciseId);
    if (exercise) {
      return exercise;
    }
  }
  return undefined;
}

// Functions to get related data
export async function getYearsForLevel(levelId: string): Promise<Year[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب السنوات حسب المستوى
    const years = await getYears();
    const filteredYears = years.filter(year => year.levelId === levelId);

    if (filteredYears.length === 0) {
      // إذا لم نجد سنوات في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      return await fetchYearsForLevel(levelId);
    }

    return filteredYears;
  } catch (error) {
    console.error(`خطأ في جلب سنوات المستوى ${levelId}:`, error);
    return [];
  }
}

export async function getSubjectsForYear(yearId: string): Promise<Subject[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب المواد حسب السنة
    const subjects = await getSubjects();
    const filteredSubjects = subjects.filter(subject => subject.yearId === yearId);

    if (filteredSubjects.length === 0) {
      // إذا لم نجد مواد في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      return await fetchSubjectsForYear(yearId);
    }

    return filteredSubjects;
  } catch (error) {
    console.error(`خطأ في جلب مواد السنة ${yearId}:`, error);
    return [];
  }
}

export async function getLessonsForSubject(subjectId: string): Promise<Lesson[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب الدروس حسب المادة
    const lessons = await getLessons();
    let filteredLessons = lessons.filter(lesson => lesson.subjectId === subjectId);

    if (filteredLessons.length === 0) {
      // إذا لم نجد دروس في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      filteredLessons = await fetchLessonsForSubject(subjectId);
    }

    // تصنيف الدروس حسب نوع المحتوى
    const exercises = filteredLessons.filter(l => l.content_type === 'tamarin');
    const homeworks = filteredLessons.filter(l => l.content_type === 'homework');
    const summaries = filteredLessons.filter(l => l.content_type === 'doros');
    const exams = filteredLessons.filter(l => l.content_type === 'exam');

    console.log("Lessons data:", filteredLessons);
    console.log("Exercises:", exercises);
    console.log("Homeworks:", homeworks);
    console.log("Summaries:", summaries);
    console.log("Exams:", exams);

    return filteredLessons;
  } catch (error) {
    console.error(`خطأ في جلب دروس المادة ${subjectId}:`, error);
    return [];
  }
}

export async function getExercisesForLesson(lessonId: string): Promise<Exercise[]> {
  const lesson = await getLesson(lessonId);
  return lesson ? lesson.exercises : [];
}

// Export all necessary functions
export {
  getLevels,
  getYears,
  getSubjects,
  getLessons
} from '../utils/dataLoader';
